import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import { join } from 'path';
// import { DefaultErrorFilter } from './filter/default.filter';
// import { NotFoundFilter } from './filter/notfound.filter';
import * as axios from '@midwayjs/axios'; // 必须引入axios组件
import { ReportMiddleware } from './middleware/report.middleware';
// 导入JWT认证组件
import jwtComponent from './component/jwt-component';
import * as sequelize from '@midwayjs/sequelize';

@Configuration({
  imports: [
    koa,
    validate,
    axios, // 必须引入@midwayjs/axios
    jwtComponent, // 引入JWT认证组件
    sequelize,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    // add middleware
    this.app.useMiddleware([ReportMiddleware]);
    // add filter
    // this.app.useFilter([NotFoundFilter, DefaultErrorFilter]);
  }
}
