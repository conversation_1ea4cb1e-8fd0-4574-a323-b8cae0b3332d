import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  Comment,
  CreatedAt,
  UpdatedAt,
  HasMany,
  Index,
} from 'sequelize-typescript';

/**
 * 问卷状态枚举
 */
export enum QuestionnaireStatus {
  DRAFT = 'draft',     // 草稿
  PUBLISHED = 'published', // 已发布
  CLOSED = 'closed',   // 已关闭
}

/**
 * 星级模式枚举
 */
export enum StarMode {
  FIVE_STAR = 5,   // 5星制
  TEN_STAR = 10,   // 10星制
}

@Table({
  tableName: 'questionnaires',
  comment: '问卷表',
  indexes: [
    {
      name: 'idx_questionnaire_school_month',
      fields: ['sso_school_id', 'month'],
    },
    {
      name: 'idx_questionnaire_status',
      fields: ['status'],
    },
    {
      name: 'idx_questionnaire_creator',
      fields: ['creator_user_id'],
    },
  ],
})
export class Questionnaire extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '问卷ID',
  })
  id: number;

  @Comment('问卷标题')
  @Column({
    type: DataType.STRING(200),
    allowNull: false,
  })
  title: string;

  @Comment('问卷描述')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @Comment('问卷月份（YYYY-MM格式）')
  @Index
  @Column({
    type: DataType.STRING(7),
    allowNull: false,
  })
  month: string;

  @Comment('问卷状态')
  @Index
  @Column({
    type: DataType.ENUM(...Object.values(QuestionnaireStatus)),
    allowNull: false,
    defaultValue: QuestionnaireStatus.DRAFT,
  })
  status: QuestionnaireStatus;

  @Comment('星级模式（5星或10星）')
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: StarMode.FIVE_STAR,
    validate: {
      isIn: [[5, 10]],
    },
  })
  star_mode: StarMode;

  @Comment('是否包含学校评价')
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  include_school_evaluation: boolean;

  @Comment('SSO学校ID')
  @Index
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
  })
  sso_school_id: string;

  @Comment('SSO学校名称')
  @Column({
    type: DataType.STRING(200),
    allowNull: true,
  })
  sso_school_name: string;

  @Comment('创建用户ID（SSO用户ID）')
  @Index
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
  })
  creator_user_id: string;

  @Comment('创建用户名称')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  creator_user_name: string;

  @Comment('问卷开始时间')
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  start_time: Date;

  @Comment('问卷结束时间')
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  end_time: Date;

  @Comment('问卷说明/须知')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  instructions: string;

  @Comment('是否允许匿名评价')
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  allow_anonymous: boolean;

  @Comment('最大评价教师数量限制（0表示无限制）')
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  max_teachers_limit: number;

  @CreatedAt
  @Comment('创建时间')
  created_at: Date;

  @UpdatedAt
  @Comment('更新时间')
  updated_at: Date;

  // 关联关系
  @HasMany(() => require('./response.entity').Response)
  responses: any[];
}
