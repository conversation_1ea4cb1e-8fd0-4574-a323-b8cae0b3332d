import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  Comment,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  Index,
} from 'sequelize-typescript';

@Table({
  tableName: 'answers',
  comment: '答案表',
  indexes: [
    {
      name: 'idx_answer_response',
      fields: ['response_id'],
    },
    {
      name: 'idx_answer_teacher',
      fields: ['sso_teacher_id'],
    },
    {
      name: 'idx_answer_rating',
      fields: ['rating'],
    },
    {
      // 唯一键：响应ID+教师ID（一个响应中每个教师只能评价一次）
      name: 'uk_answer_response_teacher',
      fields: ['response_id', 'sso_teacher_id'],
      unique: true,
    },
  ],
})
export class Answer extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '答案ID',
  })
  id: number;

  @Comment('响应ID')
  @ForeignKey(() => require('./response.entity').Response)
  @Index
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  response_id: number;

  @Comment('SSO教师ID')
  @Index
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
  })
  sso_teacher_id: string;

  @Comment('SSO教师姓名')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  sso_teacher_name: string;

  @Comment('SSO教师所属科目')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  sso_teacher_subject: string;

  @Comment('SSO教师职位')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  sso_teacher_position: string;

  @Comment('SSO教师部门')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  sso_teacher_department: string;

  @Comment('评分')
  @Index
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    validate: {
      min: 1,
      max: 10,  // 最大支持10星制
    },
  })
  rating: number;

  @Comment('文字描述/评价内容')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @Comment('评价标签（JSON格式存储多个标签）')
  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  tags: string[];

  @Comment('是否推荐该教师')
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  is_recommended: boolean;

  @Comment('教学质量评分（细分评价）')
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10,
    },
  })
  teaching_quality_rating: number;

  @Comment('教学态度评分（细分评价）')
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10,
    },
  })
  teaching_attitude_rating: number;

  @Comment('课堂管理评分（细分评价）')
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10,
    },
  })
  classroom_management_rating: number;

  @Comment('沟通能力评分（细分评价）')
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10,
    },
  })
  communication_rating: number;

  @Comment('专业知识评分（细分评价）')
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10,
    },
  })
  professional_knowledge_rating: number;

  @Comment('改进建议')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  improvement_suggestions: string;

  @Comment('最满意的方面')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  most_satisfied_aspect: string;

  @Comment('需要改进的方面')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  needs_improvement_aspect: string;

  @CreatedAt
  @Comment('创建时间')
  created_at: Date;

  @UpdatedAt
  @Comment('更新时间')
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => require('./response.entity').Response)
  response: any;
}
