import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  Comment,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
  Index,
} from 'sequelize-typescript';

@Table({
  tableName: 'responses',
  comment: '问卷响应表',
  indexes: [
    {
      name: 'idx_response_questionnaire',
      fields: ['questionnaire_id'],
    },
    {
      name: 'idx_response_phone',
      fields: ['parent_phone'],
    },
    {
      name: 'idx_response_student',
      fields: ['sso_student_id'],
    },
    {
      name: 'idx_response_month',
      fields: ['month'],
    },
    {
      // 唯一键：家长手机号+问卷ID+学生ID+月份
      name: 'uk_response_unique',
      fields: ['parent_phone', 'questionnaire_id', 'sso_student_id', 'month'],
      unique: true,
    },
  ],
})
export class Response extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '响应ID',
  })
  id: number;

  @Comment('问卷ID')
  @ForeignKey(() => require('./questionnaire.entity').Questionnaire)
  @Index
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  questionnaire_id: number;

  @Comment('家长手机号')
  @Index
  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    validate: {
      is: /^1[3-9]\d{9}$/, // 中国手机号格式验证
    },
  })
  parent_phone: string;

  @Comment('家长姓名')
  @Column({
    type: DataType.STRING(50),
    allowNull: true,
  })
  parent_name: string;

  @Comment('SSO学生ID')
  @Index
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
  })
  sso_student_id: string;

  @Comment('SSO学生姓名')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  sso_student_name: string;

  @Comment('SSO学生班级')
  @Column({
    type: DataType.STRING(100),
    allowNull: true,
  })
  sso_student_class: string;

  @Comment('SSO学生年级')
  @Column({
    type: DataType.STRING(50),
    allowNull: true,
  })
  sso_student_grade: string;

  @Comment('月份（YYYY-MM格式）')
  @Index
  @Column({
    type: DataType.STRING(7),
    allowNull: false,
  })
  month: string;

  @Comment('学校评分（如果问卷包含学校评价）')
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10,  // 最大支持10星制
    },
  })
  school_rating: number;

  @Comment('学校评价描述')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  school_description: string;

  @Comment('总评分（所有教师评分的平均值）')
  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: true,
  })
  total_rating: number;

  @Comment('评价完成状态')
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  is_completed: boolean;

  @Comment('提交时间')
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  submitted_at: Date;

  @Comment('手机验证状态')
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  phone_verified: boolean;

  @Comment('手机验证时间')
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  phone_verified_at: Date;

  @Comment('IP地址')
  @Column({
    type: DataType.STRING(45),
    allowNull: true,
  })
  ip_address: string;

  @Comment('用户代理')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  user_agent: string;

  @Comment('备注信息')
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  remarks: string;

  @CreatedAt
  @Comment('创建时间')
  created_at: Date;

  @UpdatedAt
  @Comment('更新时间')
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => require('./questionnaire.entity').Questionnaire)
  questionnaire: any;

  @HasMany(() => require('./answer.entity').Answer)
  answers: any[];
}
