/*
 * @Description: JWT认证组件API管理器
 * @Date: 2025-05-15
 */
import { HttpService } from '@midwayjs/axios';
import { Config, Inject, Provide } from '@midwayjs/core';
import { JwtLogger } from './logger';
import { JwtAuthConfig } from '../interface';

@Provide()
export class JwtApiManager {
  @Inject()
  httpService: HttpService;

  @Inject()
  logger: JwtLogger;

  @Config('jwtAuth')
  jwtAuthConfig: JwtAuthConfig;

  /**
   * 发送API请求
   * @param data 请求数据
   */
  async send(data: {
    apiCode: string;
    query?: Record<string, string | number>;
    params?: string;
    body?: Record<string, any>;
  }) {
    if (!data.apiCode) {
      throw new Error('apiCode is required');
    }

    try {
      const { status, data: sourceData } = await this.httpService.post(
        this.jwtAuthConfig.apiManagerBaseURL + this.jwtAuthConfig.apiPath,
        data
      );

      if (status !== 200) {
        this.logger.error(`API请求失败: ${status}`, { params: data });
        throw new Error('API请求失败');
      }

      const { errCode, msg, data: returnData } = sourceData;
      if (errCode !== 0) {
        this.logger.error(`API调用返回错误: ${msg}`, {
          params: data,
          response: sourceData,
        });
        throw new Error(msg);
      }

      return returnData;
    } catch (error) {
      this.logger.error('API请求异常', error);
      throw error;
    }
  }
}
