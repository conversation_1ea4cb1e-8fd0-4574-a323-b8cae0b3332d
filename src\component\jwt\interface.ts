/*
 * @Description: JWT认证组件接口定义
 * @Date: 2025-05-15
 */

/**
 * JWT令牌载荷
 */
export interface JwtTokenPayload {
  userId: string;
  username?: string;
  roles?: string[];
  type: 'access' | 'refresh';
  exp?: number;
  iat?: number;
  userCode?: string;
  enterpriseCode?: string;
}

/**
 * 用户信息
 */
export interface JwtUserInfo {
  id: string;
  userCode: string;
  username?: string;
  realName?: string;
  enterpriseCode: string;
  enterpriseName?: string;
  roles?: string[];
  [key: string]: any;
}

/**
 * 登录参数
 */
export interface JwtLoginParams {
  enterpriseCode: string;
  userCode: string;
  [key: string]: any;
}

/**
 * 登录结果
 */
export interface JwtLoginResult {
  accessToken: string;
  refreshToken: string;
  userInfo: JwtUserInfo;
}

/**
 * 刷新令牌参数
 */
export interface JwtRefreshTokenParams {
  refreshToken: string;
}

/**
 * 刷新令牌结果
 */
export interface JwtRefreshTokenResult {
  accessToken: string;
  refreshToken: string;
}

/**
 * JWT认证组件配置
 */
export interface JwtAuthConfig {
  // 是否启用
  enable: boolean;
  // 白名单路径
  whitelist: string[];
  // 令牌刷新阈值（秒）
  refreshThreshold: number;
  // 用户信息获取方法
  userInfoMethod: string;
  // 用户信息服务名称
  userInfoService: string;

  // API管理器配置
  // API管理器基础URL
  apiManagerBaseURL?: string;
  // API请求路径
  apiPath?: string;
  // 获取用户信息的API编码
  postByUserCodesApiCode?: string;
  // 解密信息的API编码
  getDataConverApiCode?: string;
  // API请求超时时间（毫秒）
  apiTimeout?: number;
}
